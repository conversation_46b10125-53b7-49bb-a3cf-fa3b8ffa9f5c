[project]
name = "deepagents"
version = "0.0.3"
description = "General purpose 'deep agent' with sub-agent spawning, todo list capabilities, and mock file system. Built on LangGraph."
readme = "README.md"
license = { text = "MIT" }
requires-python = ">=3.11,<4.0"
dependencies = [
    "langgraph>=0.2.6",
    "langchain>=0.2.14",
    "langchain-openai>=0.3.28",
    "tavily-python>=0.7.10",
    "langgraph-cli[inmem]>=0.3.6",
    "python-dotenv>=1.1.1",
]


[build-system]
requires = ["setuptools>=73.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
packages = ["deepagents"]
[tool.setuptools.package-dir]
"deepagents" = "src/deepagents"

[tool.setuptools.package-data]
"*" = ["py.typed"]
