from langchain_openai import ChatOpenAI


def get_default_model():
    return ChatOpenAI(
        model="qwen3-30b-a3b-thinking-2507",  # 或者使用 "gpt-3.5-turbo" 等其他模型
        max_tokens=10240,  # OpenAI 模型的 token 限制
        temperature=0.6,  # 可选：控制输出的随机性
        base_url="http://47.117.124.84:4033/",  # 本地 API 端点，如使用 Ollama
        api_key="sk-YyiBg6DSn1Fc2KBNU6ZYtw",  # 本地服务通常不需要真实 API key
    )

# OPENAI_API_KEY=sk-YyiBg6DSn1Fc2KBNU6ZYtw
# OPENAI_API_BASE=http://192.168.10.220:1234/v1
# OPENAI_API_BASE=http://47.117.124.84:4033/